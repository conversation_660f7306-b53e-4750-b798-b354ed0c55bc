package handlers

import (
	"net"
	"net/http"

	"github.com/nickabs/http/internal/request"
	"github.com/nickabs/http/internal/response"
)

type Handler func(w http.ResponseWriter, req *request.Request)

func RootHandler(w http.ResponseWriter, req *request.Request) {
	headers := response.GetDefaultHeaders(0)
	//response.WriteStatusLine(w, config.StatusOK)
	response.WriteHeaders(w, headers)

	// Close the connection after sending the response
	if conn, ok := w.(net.Conn); ok {
		conn.Close()
	}
}
