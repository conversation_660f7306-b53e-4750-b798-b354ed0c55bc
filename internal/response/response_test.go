package response

import (
	"bytes"
	"net/http"
	"testing"
)

// TestWriterImplementsResponseWriter verifies that Writer implements http.ResponseWriter
func TestWriterImplementsResponseWriter(t *testing.T) {
	var buf bytes.Buffer
	writer := NewWriter(&buf)

	// This line will cause a compile error if Writer doesn't implement http.ResponseWriter
	var _ http.ResponseWriter = &writer // Note: now needs pointer since methods use pointer receivers

	t.Log("Writer successfully implements http.ResponseWriter interface")
}

// TestWriterBasicFunctionality tests the basic functionality of Writer
func TestWriterBasicFunctionality(t *testing.T) {
	var buf bytes.Buffer
	writer := NewWriter(&buf)

	// Test Header() method
	header := writer.Header()
	if header == nil {
		t.Fatal("Header() returned nil")
	}

	// Set a header
	header.Set("Content-Type", "text/plain")
	header.Set("Content-Length", "13")

	// Test WriteHeader() method
	writer.WriteHeader(200)

	// Test Write() method
	data := []byte("Hello, World!")
	n, err := writer.Write(data)
	if err != nil {
		t.Fatalf("Write() failed: %v", err)
	}
	if n != len(data) {
		t.Fatalf("Write() returned %d, expected %d", n, len(data))
	}

	// Check the output
	output := buf.String()
	t.Logf("Output:\n%s", output)

	// Verify the output contains expected elements
	if !bytes.Contains(buf.Bytes(), []byte("HTTP/1.1 200 OK")) {
		t.Error("Output doesn't contain status line")
	}
	// Headers are normalized to lowercase by our headers.Headers type
	if !bytes.Contains(buf.Bytes(), []byte("content-type: text/plain")) {
		t.Error("Output doesn't contain Content-Type header")
	}
	if !bytes.Contains(buf.Bytes(), []byte("Hello, World!")) {
		t.Error("Output doesn't contain body data")
	}

	// Test that Response struct was updated
	if writer.response.StatusCode != 200 {
		t.Errorf("Response.StatusCode = %d, expected 200", writer.response.StatusCode)
	}
	if string(writer.response.Body) != "Hello, World!" {
		t.Errorf("Response.Body = %q, expected %q", string(writer.response.Body), "Hello, World!")
	}
}

// TestResponseDataStructure demonstrates the consistency with Request pattern
func TestResponseDataStructure(t *testing.T) {
	// Create a Response data structure (like Request)
	response := NewResponse()
	response.StatusCode = 201
	response.StatusLine = "HTTP/1.1 201 Created"
	response.Headers.Set("Content-Type", "application/json")
	response.Headers.Set("Location", "/api/users/123")
	response.Body = []byte(`{"id": 123, "name": "John"}`)

	// Use the Response with a Writer
	var buf bytes.Buffer
	writer := NewWriterWithResponse(&buf, &response)

	// The Writer uses the Response data
	writer.WriteHeader(response.StatusCode)
	writer.Write(response.Body)

	// Verify output
	output := buf.String()
	t.Logf("Output:\n%s", output)

	if !bytes.Contains(buf.Bytes(), []byte("HTTP/1.1 201")) {
		t.Error("Output doesn't contain correct status line")
	}
	// Headers are normalized to lowercase by our headers.Headers type
	if !bytes.Contains(buf.Bytes(), []byte("content-type: application/json")) {
		t.Error("Output doesn't contain Content-Type header")
	}
	if !bytes.Contains(buf.Bytes(), []byte(`{"id": 123, "name": "John"}`)) {
		t.Error("Output doesn't contain JSON body")
	}
}
