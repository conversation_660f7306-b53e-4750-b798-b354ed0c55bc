package response

import (
	"bytes"
	"net/http"
	"testing"
)

// TestWriterImplementsResponseWriter verifies that Writer implements http.ResponseWriter
func TestWriterImplementsResponseWriter(t *testing.T) {
	var buf bytes.Buffer
	writer := NewWriter(&buf)
	
	// This line will cause a compile error if Writer doesn't implement http.ResponseWriter
	var _ http.ResponseWriter = writer
	
	t.Log("Writer successfully implements http.ResponseWriter interface")
}

// TestWriterBasicFunctionality tests the basic functionality of Writer
func TestWriterBasicFunctionality(t *testing.T) {
	var buf bytes.Buffer
	writer := NewWriter(&buf)
	
	// Test Header() method
	header := writer.Header()
	if header == nil {
		t.<PERSON><PERSON>("Header() returned nil")
	}
	
	// Set a header
	header.Set("Content-Type", "text/plain")
	header.Set("Content-Length", "13")
	
	// Test WriteHeader() method
	writer.WriteHeader(200)
	
	// Test Write() method
	data := []byte("Hello, World!")
	n, err := writer.Write(data)
	if err != nil {
		t.Fatalf("Write() failed: %v", err)
	}
	if n != len(data) {
		t.Fatalf("Write() returned %d, expected %d", n, len(data))
	}
	
	// Check the output
	output := buf.String()
	t.Logf("Output:\n%s", output)
	
	// Verify the output contains expected elements
	if !bytes.Contains(buf.Bytes(), []byte("HTTP/1.1 200 OK")) {
		t.Error("Output doesn't contain status line")
	}
	if !bytes.Contains(buf.Bytes(), []byte("Content-Type: text/plain")) {
		t.Error("Output doesn't contain Content-Type header")
	}
	if !bytes.Contains(buf.Bytes(), []byte("Hello, World!")) {
		t.Error("Output doesn't contain body data")
	}
}
