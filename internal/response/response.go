package response

import (
	"fmt"
	"io"
	"strconv"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/headers"
)

type Response struct {
	StatusLine string
	Headers    headers.Headers
	Writer     Writer
}

type ResponseError struct {
	StatusCode int
	StatusMsg  string
}

func NewResponse() Response {
	return Response{
		Headers: headers.NewHeaders(),
	}
}

func (r *ResponseError) Write(w io.Writer) {
	msg := fmt.Sprintf("error: %v (%v)", r.StatusMsg, r.StatusCode)
	_, _ = w.Write([]byte(msg))
}

func GetDefaultHeaders(contentLen int) headers.Headers {

	headers := headers.NewHeaders()
	headers.Set("Content-length", strconv.Itoa(contentLen))
	headers.Set("Connection", "close")
	headers.Set("Content-Type", "text/plain")
	return headers
}

func getStatusLine(statusCode int) []byte {
	reasonPhrase := ""
	switch statusCode {
	case config.StatusOK:
		reasonPhrase = "OK"
	case config.StatusBadRequest:
		reasonPhrase = "Bad Request"
	case config.StatusServerError:
		reasonPhrase = "Internal Server Error"
	}
	return []byte(fmt.Sprintf("HTTP/1.1 %d %s\r\n", statusCode, reasonPhrase))
}

type Writer struct {
	writer io.Writer
}

func NewWriter(w io.Writer) Writer {
	return Writer{
		writer: w,
	}

}

func (w Writer) Headers() headers.Headers {
	return w.Headers
}

func (w Writer) WriteStatusLine(statusCode int) error {

	statusLine := ""
	switch statusCode {
	case config.StatusOK:
		statusLine = "HTTP/1.1 200 OK"
	case config.StatusBadRequest:
		statusLine = "HTTP/1.1 400 Bad Request"
	case config.StatusServerError:
		statusLine = "HTTP/1.1 400 Server Error"
	default:
		return fmt.Errorf("invalid status code: %v", statusCode)
	}

	_, err := w.writer.Write([]byte(statusLine + "\r\n"))

	return err
}

func WriteHeaders(w io.Writer, headers headers.Headers) error {
	for k, v := range headers {
		_, err := fmt.Fprintf(w, "%v: %v\r\n", k, v)
		if err != nil {
			return err
		}
	}

	// close headers
	_, err := w.Write([]byte("\r\n"))
	return err
}
