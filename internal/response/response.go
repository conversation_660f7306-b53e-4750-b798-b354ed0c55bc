package response

import (
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/headers"
)

type Response struct {
	StatusLine string
	Headers    headers.Headers
	Writer     Writer
}

type ResponseError struct {
	StatusCode int
	StatusMsg  string
}

func NewResponse() Response {
	return Response{
		Headers: headers.NewHeaders(),
	}
}

func (r *ResponseError) Write(w io.Writer) {
	msg := fmt.Sprintf("error: %v (%v)", r.StatusMsg, r.StatusCode)
	_, _ = w.Write([]byte(msg))
}

func GetDefaultHeaders(contentLen int) headers.Headers {

	headers := headers.NewHeaders()
	headers.Set("Content-length", strconv.Itoa(contentLen))
	headers.Set("Connection", "close")
	headers.Set("Content-Type", "text/plain")
	return headers
}

func getStatusLine(statusCode int) []byte {
	reasonPhrase := ""
	switch statusCode {
	case config.StatusOK:
		reasonPhrase = "OK"
	case config.StatusBadRequest:
		reasonPhrase = "Bad Request"
	case config.StatusServerError:
		reasonPhrase = "Internal Server Error"
	}
	return []byte(fmt.Sprintf("HTTP/1.1 %d %s\r\n", statusCode, reasonPhrase))
}

type Writer struct {
	writer  io.Writer
	headers http.Header
}

func NewWriter(w io.Writer) Writer {
	return Writer{
		writer:  w,
		headers: make(http.Header),
	}
}

// Header returns the header map that will be sent by WriteHeader.
// Changing the header after a call to WriteHeader (or Write) has no effect.
func (w Writer) Header() http.Header {
	return w.headers
}

// Write writes the data to the connection as part of an HTTP reply.
func (w Writer) Write(data []byte) (int, error) {
	return w.writer.Write(data)
}

// WriteHeader sends an HTTP response header with the provided status code.
func (w Writer) WriteHeader(statusCode int) {
	statusLine := ""
	switch statusCode {
	case config.StatusOK:
		statusLine = "HTTP/1.1 200 OK"
	case config.StatusBadRequest:
		statusLine = "HTTP/1.1 400 Bad Request"
	case config.StatusServerError:
		statusLine = "HTTP/1.1 500 Internal Server Error"
	default:
		statusLine = fmt.Sprintf("HTTP/1.1 %d Unknown", statusCode)
	}

	// Write status line
	w.writer.Write([]byte(statusLine + "\r\n"))

	// Write headers
	for key, values := range w.headers {
		for _, value := range values {
			w.writer.Write([]byte(fmt.Sprintf("%s: %s\r\n", key, value)))
		}
	}

	// End headers with empty line
	w.writer.Write([]byte("\r\n"))
}

func (w Writer) WriteStatusLine(statusCode int) error {

	statusLine := ""
	switch statusCode {
	case config.StatusOK:
		statusLine = "HTTP/1.1 200 OK"
	case config.StatusBadRequest:
		statusLine = "HTTP/1.1 400 Bad Request"
	case config.StatusServerError:
		statusLine = "HTTP/1.1 400 Server Error"
	default:
		return fmt.Errorf("invalid status code: %v", statusCode)
	}

	_, err := w.writer.Write([]byte(statusLine + "\r\n"))

	return err
}

func WriteHeaders(w io.Writer, headers headers.Headers) error {
	for k, v := range headers {
		_, err := fmt.Fprintf(w, "%v: %v\r\n", k, v)
		if err != nil {
			return err
		}
	}

	// close headers
	_, err := w.Write([]byte("\r\n"))
	return err
}
