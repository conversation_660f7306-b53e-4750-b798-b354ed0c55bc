package response

import (
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/headers"
)

// Response represents an HTTP response data structure (mirrors Request struct)
type Response struct {
	StatusLine string
	Headers    headers.Headers
	Body       []byte
	StatusCode int
}

type ResponseError struct {
	StatusCode int
	StatusMsg  string
}

func NewResponse() Response {
	return Response{
		Headers:    headers.NewHeaders(),
		Body:       make([]byte, 0),
		StatusCode: config.StatusOK,
	}
}

func (r *ResponseError) Write(w io.Writer) {
	msg := fmt.Sprintf("error: %v (%v)", r.StatusMsg, r.StatusCode)
	_, _ = w.Write([]byte(msg))
}

func GetDefaultHeaders(contentLen int) headers.Headers {

	headers := headers.NewHeaders()
	headers.Set("Content-length", strconv.Itoa(contentLen))
	headers.Set("Connection", "close")
	headers.Set("Content-Type", "text/plain")
	return headers
}

func getStatusLine(statusCode int) []byte {
	reasonPhrase := ""
	switch statusCode {
	case config.StatusOK:
		reasonPhrase = "OK"
	case config.StatusBadRequest:
		reasonPhrase = "Bad Request"
	case config.StatusServerError:
		reasonPhrase = "Internal Server Error"
	}
	return []byte(fmt.Sprintf("HTTP/1.1 %d %s\r\n", statusCode, reasonPhrase))
}

// Writer implements http.ResponseWriter interface and writes Response data
type Writer struct {
	writer      io.Writer
	response    *Response
	headersSent bool
	httpHeaders http.Header // Cache for http.Header conversion
}

func NewWriter(w io.Writer) Writer {
	response := NewResponse()
	return Writer{
		writer:      w,
		response:    &response,
		headersSent: false,
		httpHeaders: make(http.Header),
	}
}

// NewWriterWithResponse creates a Writer with an existing Response
func NewWriterWithResponse(w io.Writer, response *Response) Writer {
	return Writer{
		writer:      w,
		response:    response,
		headersSent: false,
		httpHeaders: make(http.Header),
	}
}

// Header returns the header map that will be sent by WriteHeader.
// This syncs between our custom headers.Headers and http.Header
func (w *Writer) Header() http.Header {
	// Sync from our Response.Headers to http.Header format
	w.httpHeaders = make(http.Header)
	for key, value := range w.response.Headers {
		w.httpHeaders.Set(key, value)
	}
	return w.httpHeaders
}

// Write writes the data to the connection as part of an HTTP reply.
func (w *Writer) Write(data []byte) (int, error) {
	// If headers haven't been sent yet, send them with 200 OK
	if !w.headersSent {
		w.WriteHeader(config.StatusOK)
	}

	// Append to response body for record keeping
	w.response.Body = append(w.response.Body, data...)

	// Write to the actual connection
	return w.writer.Write(data)
}

// WriteHeader sends an HTTP response header with the provided status code.
func (w *Writer) WriteHeader(statusCode int) {
	if w.headersSent {
		return // Headers already sent
	}

	// Update response status
	w.response.StatusCode = statusCode

	// Sync any changes from http.Header back to our Response.Headers
	for key, values := range w.httpHeaders {
		if len(values) > 0 {
			w.response.Headers.Set(key, values[0]) // Our headers.Headers only supports single values
		}
	}

	statusLine := ""
	switch statusCode {
	case config.StatusOK:
		statusLine = "HTTP/1.1 200 OK"
	case 201:
		statusLine = "HTTP/1.1 201 Created"
	case config.StatusBadRequest:
		statusLine = "HTTP/1.1 400 Bad Request"
	case config.StatusServerError:
		statusLine = "HTTP/1.1 500 Internal Server Error"
	default:
		statusLine = fmt.Sprintf("HTTP/1.1 %d Unknown", statusCode)
	}

	w.response.StatusLine = statusLine

	// Write status line
	w.writer.Write([]byte(statusLine + "\r\n"))

	// Write headers from Response.Headers
	for key, value := range w.response.Headers {
		w.writer.Write([]byte(fmt.Sprintf("%s: %s\r\n", key, value)))
	}

	// End headers with empty line
	w.writer.Write([]byte("\r\n"))
	w.headersSent = true
}

func (w Writer) WriteStatusLine(statusCode int) error {

	statusLine := ""
	switch statusCode {
	case config.StatusOK:
		statusLine = "HTTP/1.1 200 OK"
	case config.StatusBadRequest:
		statusLine = "HTTP/1.1 400 Bad Request"
	case config.StatusServerError:
		statusLine = "HTTP/1.1 400 Server Error"
	default:
		return fmt.Errorf("invalid status code: %v", statusCode)
	}

	_, err := w.writer.Write([]byte(statusLine + "\r\n"))

	return err
}

func WriteHeaders(w io.Writer, headers headers.Headers) error {
	for k, v := range headers {
		_, err := fmt.Fprintf(w, "%v: %v\r\n", k, v)
		if err != nil {
			return err
		}
	}

	// close headers
	_, err := w.Write([]byte("\r\n"))
	return err
}
