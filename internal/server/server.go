package server

import (
	"context"
	"fmt"
	"log"
	"net"

	"github.com/nickabs/http/internal/config"
	"github.com/nickabs/http/internal/handlers"
	"github.com/nickabs/http/internal/request"
	"github.com/nickabs/http/internal/response"
)

type Server struct {
	addr     string
	listener net.Listener
	handler  handlers.Handler
}

// New creates a new server instance without starting it
func New(port int) *Server {
	return &Server{
		handler: handlers.RootHandler,
		addr:    fmt.Sprintf(":%d", port),
	}
}

// Start begins listening for connections and blocks until context is cancelled
func (s *Server) Start(ctx context.Context) error {
	listener, err := net.Listen("tcp", s.addr)
	if err != nil {
		return fmt.Errorf("failed to start listener on %s: %w", s.addr, err)
	}
	s.listener = listener

	log.Printf("Server listening on %s", s.addr)

	// Handle shutdown gracefully
	go func() {
		<-ctx.Done()
		log.Println("Shutting down server...")
		s.listener.Close()
	}()

	// Accept connections
	for {
		conn, err := listener.Accept()
		if err != nil {
			// Check if we're shutting down
			select {
			case <-ctx.Done():
				return nil
			default:
				log.Printf("Error accepting connection: %v", err)
				continue
			}
		}

		req := request.NewRequest()

		log.Printf("got request from %v", conn.RemoteAddr())
		err = req.ReadRequest(conn)
		if err != nil {
			re := response.ResponseError{
				StatusCode: config.StatusBadRequest,
				StatusMsg:  err.Error(),
			}
			log.Printf("could not read request %v", err)
			re.Write(conn)
			conn.Close()
			continue
		}

		if req.RequestLine.RequestTarget != "/root" {
			re := response.ResponseError{
				StatusCode: config.StatusNotFound,
				StatusMsg:  "Not found",
			}
			log.Printf("route not found %v", req.RequestLine.RequestTarget)
			re.Write(conn)
			conn.Close()
			continue
		}

		// Handle each connection in a goroutine
		// Create a ResponseWriter from the connection
		writer := response.NewWriter(conn)
		go s.handler(&writer, &req)
	}
}
