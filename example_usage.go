package main

import (
	"bytes"
	"fmt"
	"net/http"
	"strings"

	"github.com/nickabs/http/internal/request"
	"github.com/nickabs/http/internal/response"
)

// This example demonstrates the symmetry between Request and Response structures
func main() {
	fmt.Println("=== Request/Response Architecture Demonstration ===\n")

	// === REQUEST SIDE ===
	fmt.Println("1. REQUEST SIDE (Data Structure Pattern):")
	
	// Create a Request data structure (your existing pattern)
	req := request.NewRequest()
	
	// Simulate parsing HTTP request data
	httpRequestData := "GET /api/users HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/json\r\n\r\n"
	reader := strings.NewReader(httpRequestData)
	err := req.ReadRequest(reader)
	if err != nil {
		fmt.Printf("Error parsing request: %v\n", err)
		return
	}
	
	fmt.Printf("  Method: %s\n", req.RequestLine.Method)
	fmt.Printf("  Target: %s\n", req.RequestLine.RequestTarget)
	fmt.Printf("  Headers: %v\n", req.Headers)
	fmt.Printf("  Body: %s\n", string(req.Body))

	// === RESPONSE SIDE ===
	fmt.Println("\n2. RESPONSE SIDE (Matching Data Structure Pattern):")
	
	// Create a Response data structure (mirrors Request pattern)
	resp := response.NewResponse()
	resp.StatusCode = 200
	resp.StatusLine = "HTTP/1.1 200 OK"
	resp.Headers.Set("Content-Type", "application/json")
	resp.Headers.Set("Content-Length", "25")
	resp.Body = []byte(`{"users": ["Alice", "Bob"]}`)
	
	fmt.Printf("  StatusCode: %d\n", resp.StatusCode)
	fmt.Printf("  StatusLine: %s\n", resp.StatusLine)
	fmt.Printf("  Headers: %v\n", resp.Headers)
	fmt.Printf("  Body: %s\n", string(resp.Body))

	// === HTTP INTERFACE IMPLEMENTATION ===
	fmt.Println("\n3. HTTP INTERFACE IMPLEMENTATION:")
	
	// Use Writer to implement http.ResponseWriter interface
	var buf bytes.Buffer
	writer := response.NewWriterWithResponse(&buf, &resp)
	
	// This demonstrates how Writer implements http.ResponseWriter
	// and can be used in standard HTTP handlers
	simulateHTTPHandler(&writer, &req)
	
	fmt.Printf("  HTTP Output:\n%s", buf.String())

	// === SYMMETRY DEMONSTRATION ===
	fmt.Println("\n4. ARCHITECTURE SYMMETRY:")
	fmt.Println("  Request struct  -> Data structure for parsed HTTP requests")
	fmt.Println("  Response struct -> Data structure for HTTP responses (mirrors Request)")
	fmt.Println("  Writer struct   -> Interface implementation (http.ResponseWriter)")
	fmt.Println("  Both use the same headers.Headers type for consistency")
}

// simulateHTTPHandler demonstrates how Writer can be used as http.ResponseWriter
func simulateHTTPHandler(w http.ResponseWriter, req *request.Request) {
	// This is how you'd use it in a real HTTP handler
	// func MyHandler(w http.ResponseWriter, r *http.Request)
	
	// Set headers using standard http.ResponseWriter interface
	w.Header().Set("X-Custom-Header", "custom-value")
	
	// Write response
	w.WriteHeader(200)
	w.Write([]byte("Additional data from handler"))
}
